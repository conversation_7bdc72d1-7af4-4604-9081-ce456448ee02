<!--
{{RIPER-5+SMART-6:
  Action: "Hybrid-Architecture"
  Task_ID: "PDFJS-ENHANCED-VIEWER"
  Timestamp: "2025-08-18T17:40:36+08:00"
  Authoring_Subagent: "architecture-expert"
  Principle_Applied: "智能切换与渐进增强原则"
  Quality_Check: "完整的混合架构，支持官方查看器和Canvas方案智能切换。"
}}
-->

<template>
  <div class="pdf-viewer-enhanced">
    <!-- 策略选择器 (开发模式) -->
    <div v-if="showStrategySelector" class="strategy-selector">
      <el-radio-group v-model="selectedStrategy" @change="switchStrategy">
        <el-radio-button value="official">官方查看器</el-radio-button>
        <el-radio-button value="canvas">Canvas方案</el-radio-button>
        <el-radio-button value="auto">智能选择</el-radio-button>
      </el-radio-group>
      
      <el-tag :type="strategyTagType" class="strategy-tag">
        当前: {{ strategyDisplayName }}
      </el-tag>
    </div>

    <!-- 文档信息面板 -->
    <div v-if="documentInfo" class="document-info">
      <el-descriptions :column="4" size="small" border>
        <el-descriptions-item label="页数">{{ documentInfo.totalPages }}</el-descriptions-item>
        <el-descriptions-item label="文件大小">{{ formatFileSize(documentInfo.fileSize) }}</el-descriptions-item>
        <el-descriptions-item label="渲染策略">{{ strategyDisplayName }}</el-descriptions-item>
        <el-descriptions-item label="性能模式">{{ performanceMode }}</el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 动态查看器组件 -->
    <component
      :is="currentViewerComponent"
      v-bind="viewerProps"
      @page-changed="onPageChanged"
      @document-loaded="onDocumentLoaded"
      @text-selected="onTextSelected"
      @error="onViewerError"
      class="viewer-component"
    />

    <!-- 性能监控面板 (开发模式) -->
    <div v-if="showPerformanceMonitor && performanceMetrics" class="performance-monitor">
      <el-card header="性能监控">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-statistic title="加载时间" :value="performanceMetrics.loadTime" suffix="ms" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="内存使用" :value="performanceMetrics.memoryUsage" suffix="MB" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="渲染时间" :value="performanceMetrics.renderTime" suffix="ms" />
          </el-col>
          <el-col :span="6">
            <el-statistic title="缓存命中率" :value="performanceMetrics.cacheHitRate" suffix="%" />
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 错误恢复面板 -->
    <div v-if="fallbackError" class="fallback-error">
      <el-alert
        title="查看器加载失败"
        :description="fallbackError"
        type="error"
        show-icon
        :closable="false"
      >
        <template #default>
          <el-button @click="retryWithFallback" type="primary" size="small">
            尝试备用方案
          </el-button>
          <el-button @click="resetViewer" size="small">
            重置查看器
          </el-button>
        </template>
      </el-alert>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, shallowRef } from 'vue'
import { ElMessage } from 'element-plus'
import PdfViewerOfficial from './PdfViewerOfficial.vue'
import DocumentPdfViewer from './DocumentPdfViewer.vue'

// {{RIPER-5+SMART-6:
//   Action: "Strategy-Pattern"
//   Task_ID: "VIEWER-STRATEGY"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "design-pattern-expert"
//   Principle_Applied: "策略模式与智能选择原则"
//   Quality_Check: "完整的策略模式实现，支持动态切换和自动选择。"
// }}

type ViewerStrategy = 'official' | 'canvas' | 'auto'
type PerformanceMode = 'standard' | 'optimized' | 'memory-saver'

interface DocumentInfo {
  totalPages: number
  fileSize: number
  hasAnnotations: boolean
  hasComplexGraphics: boolean
  requiresEditing: boolean
}

interface PerformanceMetrics {
  loadTime: number
  memoryUsage: number
  renderTime: number
  cacheHitRate: number
}

interface ViewerConfig {
  strategy?: ViewerStrategy
  performanceMode?: PerformanceMode
  enableAnnotations?: boolean
  enableCustomToolbar?: boolean
  height?: string | number
  width?: string | number
}

// Props定义
const props = withDefaults(defineProps<{
  pdfUrl: string
  config?: ViewerConfig
  showStrategySelector?: boolean
  showPerformanceMonitor?: boolean
}>(), {
  config: () => ({}),
  showStrategySelector: import.meta.env.DEV,
  showPerformanceMonitor: import.meta.env.DEV
})

// Emits定义
const emit = defineEmits<{
  pageChanged: [pageNumber: number]
  documentLoaded: [totalPages: number]
  textSelected: [selectedText: string]
  strategyChanged: [strategy: ViewerStrategy]
  error: [error: string]
}>()

// 响应式状态
const selectedStrategy = ref<ViewerStrategy>(props.config.strategy ?? 'auto')
const currentStrategy = ref<ViewerStrategy>('official')
const documentInfo = ref<DocumentInfo>()
const performanceMetrics = ref<PerformanceMetrics>()
const fallbackError = ref('')
const isAnalyzing = ref(false)

// 动态组件引用
const currentViewerComponent = shallowRef(PdfViewerOfficial)

// 计算属性
const strategyDisplayName = computed(() => {
  const names = {
    official: 'PDF.js官方查看器',
    canvas: 'Canvas自定义方案',
    auto: '智能自动选择'
  }
  return names[currentStrategy.value]
})

const strategyTagType = computed(() => {
  const types = {
    official: 'success',
    canvas: 'warning',
    auto: 'info'
  }
  return types[currentStrategy.value] as any
})

const performanceMode = computed(() => {
  if (!documentInfo.value) return 'standard'
  
  const { totalPages, fileSize } = documentInfo.value
  
  if (totalPages > 200 || fileSize > 50 * 1024 * 1024) {
    return 'memory-saver'
  } else if (totalPages > 50 || fileSize > 10 * 1024 * 1024) {
    return 'optimized'
  }
  return 'standard'
})

const viewerProps = computed(() => ({
  pdfUrl: props.pdfUrl,
  config: {
    ...props.config,
    performanceMode: performanceMode.value
  },
  showCustomToolbar: props.config.enableCustomToolbar ?? true,
  height: props.config.height ?? '600px',
  width: props.config.width ?? '100%'
}))

// {{RIPER-5+SMART-6:
//   Action: "Intelligent-Selection"
//   Task_ID: "AUTO-STRATEGY"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "ai-decision-expert"
//   Principle_Applied: "智能决策算法原则"
//   Quality_Check: "基于文档特征的智能策略选择算法。"
// }}

// 智能策略选择算法
const selectOptimalStrategy = async (docInfo: DocumentInfo): Promise<ViewerStrategy> => {
  const { totalPages, fileSize, hasAnnotations, hasComplexGraphics, requiresEditing } = docInfo
  
  // 评分系统
  let officialScore = 0
  let canvasScore = 0
  
  // 功能需求评分
  if (hasAnnotations || requiresEditing) {
    officialScore += 50 // 官方查看器支持注释编辑
  }
  
  if (hasComplexGraphics) {
    officialScore += 30 // 官方查看器渲染复杂图形更稳定
  }
  
  // 性能需求评分
  if (totalPages > 200) {
    officialScore += 40 // 大文档官方查看器性能更好
  } else if (totalPages < 20) {
    canvasScore += 20 // 小文档Canvas方案响应更快
  }
  
  if (fileSize > 50 * 1024 * 1024) {
    officialScore += 30 // 大文件官方查看器内存管理更好
  }
  
  // 定制需求评分
  if (props.config.enableCustomToolbar) {
    canvasScore += 20 // Canvas方案定制性更强
  }
  
  // 兼容性评分
  officialScore += 20 // 官方查看器兼容性更好
  
  console.log(`策略评分 - 官方: ${officialScore}, Canvas: ${canvasScore}`)
  
  return officialScore >= canvasScore ? 'official' : 'canvas'
}

// 分析文档特征
const analyzeDocument = async (url: string): Promise<DocumentInfo> => {
  isAnalyzing.value = true
  
  try {
    // 模拟文档分析 (实际项目中可以调用后端API)
    const response = await fetch(url, { method: 'HEAD' })
    const fileSize = parseInt(response.headers.get('content-length') || '0')
    
    // 这里可以添加更复杂的文档分析逻辑
    // 例如：解析PDF元数据、检测注释、分析图形复杂度等
    
    return {
      totalPages: 100, // 临时值，实际应从PDF解析获取
      fileSize,
      hasAnnotations: false,
      hasComplexGraphics: false,
      requiresEditing: false
    }
  } catch (error) {
    console.warn('文档分析失败，使用默认配置:', error)
    return {
      totalPages: 50,
      fileSize: 5 * 1024 * 1024,
      hasAnnotations: false,
      hasComplexGraphics: false,
      requiresEditing: false
    }
  } finally {
    isAnalyzing.value = false
  }
}

// 切换查看器策略
const switchStrategy = async (strategy: ViewerStrategy) => {
  try {
    let targetStrategy = strategy
    
    if (strategy === 'auto' && documentInfo.value) {
      targetStrategy = await selectOptimalStrategy(documentInfo.value)
    }
    
    // 更新组件
    if (targetStrategy === 'official') {
      currentViewerComponent.value = PdfViewerOfficial
    } else {
      currentViewerComponent.value = DocumentPdfViewer
    }
    
    currentStrategy.value = targetStrategy
    emit('strategyChanged', targetStrategy)
    
    ElMessage.success(`已切换到${strategyDisplayName.value}`)
    
  } catch (error) {
    console.error('策略切换失败:', error)
    ElMessage.error('策略切换失败，请重试')
  }
}

// 事件处理
const onPageChanged = (pageNumber: number) => {
  emit('pageChanged', pageNumber)
}

const onDocumentLoaded = (totalPages: number) => {
  if (documentInfo.value) {
    documentInfo.value.totalPages = totalPages
  }
  emit('documentLoaded', totalPages)
}

const onTextSelected = (selectedText: string) => {
  emit('textSelected', selectedText)
}

const onViewerError = (error: string) => {
  fallbackError.value = error
  emit('error', error)
}

// 错误恢复
const retryWithFallback = async () => {
  const fallbackStrategy = currentStrategy.value === 'official' ? 'canvas' : 'official'
  await switchStrategy(fallbackStrategy)
  fallbackError.value = ''
}

const resetViewer = () => {
  fallbackError.value = ''
  selectedStrategy.value = 'auto'
  switchStrategy('auto')
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 监听URL变化
watch(() => props.pdfUrl, async (newUrl) => {
  if (newUrl) {
    documentInfo.value = await analyzeDocument(newUrl)
    
    if (selectedStrategy.value === 'auto') {
      await switchStrategy('auto')
    }
  }
}, { immediate: true })

// 监听策略变化
watch(selectedStrategy, switchStrategy)

// 生命周期
onMounted(async () => {
  if (props.pdfUrl) {
    documentInfo.value = await analyzeDocument(props.pdfUrl)
    
    if (selectedStrategy.value === 'auto') {
      await switchStrategy('auto')
    }
  }
})

// 暴露方法
defineExpose({
  switchStrategy,
  getCurrentStrategy: () => currentStrategy.value,
  getDocumentInfo: () => documentInfo.value,
  retryWithFallback,
  resetViewer
})
</script>

<style scoped lang="scss">
.pdf-viewer-enhanced {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.strategy-selector {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;

  .strategy-tag {
    margin-left: auto;
  }
}

.document-info {
  padding: 0 12px;
}

.viewer-component {
  flex: 1;
  min-height: 0;
}

.performance-monitor {
  padding: 0 12px;
}

.fallback-error {
  padding: 0 12px;
}

// 响应式设计
@media (max-width: 768px) {
  .strategy-selector {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;

    .strategy-tag {
      margin-left: 0;
      align-self: center;
    }
  }
}
</style>
