<!--
{{RIPER-5+SMART-6:
  Action: "Component-Creation"
  Task_ID: "PDFJS-OFFICIAL-VIEWER"
  Timestamp: "2025-08-18T17:40:36+08:00"
  Authoring_Subagent: "vue-component-expert"
  Principle_Applied: "PDF.js官方查看器集成原则"
  Quality_Check: "完整的iframe集成，支持事件通信和配置定制。"
}}
-->

<template>
  <div class="pdf-viewer-official" :class="{ 'fullscreen': isFullscreen }">
    <!-- 自定义工具栏 (可选) -->
    <div v-if="showCustomToolbar" class="custom-toolbar">
      <el-button-group>
        <el-button @click="goToPreviousPage" :disabled="currentPage <= 1">
          <el-icon><ArrowLeft /></el-icon>
          上一页
        </el-button>
        <el-button @click="goToNextPage" :disabled="currentPage >= totalPages">
          下一页
          <el-icon><ArrowRight /></el-icon>
        </el-button>
      </el-button-group>
      
      <el-input-number
        v-model="currentPage"
        :min="1"
        :max="totalPages"
        @change="goToPage"
        class="page-input"
      />
      <span class="page-info">/ {{ totalPages }}</span>
      
      <el-button @click="toggleFullscreen">
        <el-icon><FullScreen /></el-icon>
        {{ isFullscreen ? '退出全屏' : '全屏' }}
      </el-button>
    </div>

    <!-- PDF.js官方查看器iframe -->
    <iframe
      ref="pdfViewerFrame"
      :src="viewerUrl"
      class="pdf-viewer-iframe"
      :style="iframeStyle"
      @load="onViewerLoad"
      frameborder="0"
      allowfullscreen
    />

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-overlay">
      <el-loading-spinner />
      <p>正在加载PDF文档...</p>
    </div>

    <!-- 错误状态 -->
    <div v-if="error" class="error-overlay">
      <el-icon class="error-icon"><WarningFilled /></el-icon>
      <p>{{ error }}</p>
      <el-button @click="retry">重试</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowLeft, ArrowRight, FullScreen, WarningFilled } from '@element-plus/icons-vue'
import { ensurePdfJsInitialized, checkPdfJsAvailability } from '@/utils/pdfjs-config'

// {{RIPER-5+SMART-6:
//   Action: "TypeScript-Integration"
//   Task_ID: "PDFJS-TYPES"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "typescript-expert"
//   Principle_Applied: "类型安全原则"
//   Quality_Check: "完整的TypeScript类型定义和接口。"
// }}

interface PdfViewerConfig {
  disablePresentationMode?: boolean
  disableOpenFile?: boolean
  disablePrint?: boolean
  disableDownload?: boolean
  disableBookmark?: boolean
  disableEditing?: boolean
  enableAnnotations?: boolean
  locale?: string
}

interface PdfViewerEvents {
  pageChanged: (pageNumber: number) => void
  documentLoaded: (totalPages: number) => void
  textSelected: (selectedText: string) => void
  error: (error: string) => void
}

// Props定义
const props = withDefaults(defineProps<{
  pdfUrl: string
  config?: PdfViewerConfig
  showCustomToolbar?: boolean
  height?: string | number
  width?: string | number
}>(), {
  config: () => ({}),
  showCustomToolbar: false,
  height: '600px',
  width: '100%'
})

// Emits定义
const emit = defineEmits<PdfViewerEvents>()

// 响应式状态
const pdfViewerFrame = ref<HTMLIFrameElement>()
const loading = ref(true)
const error = ref('')
const currentPage = ref(1)
const totalPages = ref(0)
const isFullscreen = ref(false)

// 计算属性
const viewerUrl = computed(() => {
  const baseUrl = import.meta.env.PROD 
    ? '/pdfjs/web/viewer.html' 
    : '/node_modules/pdfjs-dist/web/viewer.html'
  
  const params = new URLSearchParams({
    file: encodeURIComponent(props.pdfUrl),
    // 默认配置
    disablepresentationmode: String(props.config.disablePresentationMode ?? true),
    disableopenfile: String(props.config.disableOpenFile ?? true),
    disableprint: String(props.config.disablePrint ?? false),
    disabledownload: String(props.config.disableDownload ?? false),
    disablebookmark: String(props.config.disableBookmark ?? false),
    disableediting: String(props.config.disableEditing ?? false),
    // 语言设置
    locale: props.config.locale ?? 'zh-CN'
  })
  
  return `${baseUrl}?${params.toString()}`
})

const iframeStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height
}))

// {{RIPER-5+SMART-6:
//   Action: "Event-Communication"
//   Task_ID: "IFRAME-POSTMESSAGE"
//   Timestamp: "2025-08-18T17:40:36+08:00"
//   Authoring_Subagent: "iframe-communication-expert"
//   Principle_Applied: "安全的跨域通信原则"
//   Quality_Check: "完整的postMessage事件处理和错误处理。"
// }}

// iframe加载完成处理
const onViewerLoad = () => {
  try {
    const iframe = pdfViewerFrame.value
    if (!iframe?.contentWindow) {
      throw new Error('无法访问iframe内容')
    }

    // 监听PDF.js事件
    window.addEventListener('message', handlePdfJsMessage)
    
    // 发送初始化消息
    iframe.contentWindow.postMessage({
      type: 'INIT_VIEWER',
      config: props.config
    }, '*')

    loading.value = false
    
  } catch (err) {
    error.value = `查看器加载失败: ${err instanceof Error ? err.message : '未知错误'}`
    loading.value = false
  }
}

// 处理PDF.js消息
const handlePdfJsMessage = (event: MessageEvent) => {
  // 安全检查：确保消息来源正确
  if (!event.origin.includes(window.location.origin) && 
      !event.data?.type?.startsWith('PDF_')) {
    return
  }

  const { type, data } = event.data

  switch (type) {
    case 'PDF_PAGE_CHANGED':
      currentPage.value = data.pageNumber
      emit('pageChanged', data.pageNumber)
      break
      
    case 'PDF_DOCUMENT_LOADED':
      totalPages.value = data.totalPages
      emit('documentLoaded', data.totalPages)
      break
      
    case 'PDF_TEXT_SELECTED':
      emit('textSelected', data.selectedText)
      break
      
    case 'PDF_ERROR':
      error.value = data.message
      emit('error', data.message)
      break
      
    default:
      // 忽略未知消息类型
      break
  }
}

// 页面导航方法
const goToPage = (pageNumber: number) => {
  if (pageNumber < 1 || pageNumber > totalPages.value) return
  
  const iframe = pdfViewerFrame.value
  if (iframe?.contentWindow) {
    iframe.contentWindow.postMessage({
      type: 'GO_TO_PAGE',
      pageNumber
    }, '*')
  }
}

const goToPreviousPage = () => {
  if (currentPage.value > 1) {
    goToPage(currentPage.value - 1)
  }
}

const goToNextPage = () => {
  if (currentPage.value < totalPages.value) {
    goToPage(currentPage.value + 1)
  }
}

// 全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value
  
  if (isFullscreen.value) {
    document.documentElement.requestFullscreen?.()
  } else {
    document.exitFullscreen?.()
  }
}

// 重试加载
const retry = () => {
  error.value = ''
  loading.value = true
  
  // 重新加载iframe
  const iframe = pdfViewerFrame.value
  if (iframe) {
    iframe.src = iframe.src
  }
}

// 监听URL变化
watch(() => props.pdfUrl, () => {
  if (props.pdfUrl) {
    loading.value = true
    error.value = ''
  }
})

// 生命周期
onMounted(async () => {
  // 确保PDF.js正确初始化
  try {
    const isInitialized = await ensurePdfJsInitialized()
    if (!isInitialized) {
      console.error('❌ PDF.js初始化失败')
      error.value = 'PDF.js初始化失败，请刷新页面重试'
      return
    }

    if (!checkPdfJsAvailability()) {
      console.error('❌ PDF.js不可用')
      error.value = 'PDF查看器不可用，请检查网络连接'
      return
    }

    console.log('✅ PDF.js官方查看器初始化成功')
  } catch (initError) {
    console.error('❌ PDF.js初始化异常:', initError)
    error.value = 'PDF查看器初始化异常，请刷新页面重试'
    return
  }

  // 监听全屏变化
  document.addEventListener('fullscreenchange', () => {
    isFullscreen.value = !!document.fullscreenElement
  })
})

onUnmounted(() => {
  window.removeEventListener('message', handlePdfJsMessage)
  document.removeEventListener('fullscreenchange', () => {})
})

// 暴露方法给父组件
defineExpose({
  goToPage,
  goToPreviousPage,
  goToNextPage,
  toggleFullscreen,
  retry,
  getCurrentPage: () => currentPage.value,
  getTotalPages: () => totalPages.value
})
</script>

<style scoped lang="scss">
.pdf-viewer-official {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;

  &.fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: #fff;
  }
}

.custom-toolbar {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .page-input {
    width: 80px;
  }

  .page-info {
    color: #606266;
    font-size: 14px;
  }
}

.pdf-viewer-iframe {
  flex: 1;
  border: none;
  background: #fff;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;

  p {
    margin: 16px 0;
    color: #606266;
    font-size: 14px;
  }
}

.error-overlay {
  .error-icon {
    font-size: 48px;
    color: #f56c6c;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .custom-toolbar {
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px;

    .page-input {
      width: 60px;
    }
  }
}
</style>
