/**
 * {{RIPER-5+SMART-6:
 *   Action: "PDF.js-Configuration"
 *   Task_ID: "PDFJS-WORKER-CONFIG"
 *   Timestamp: "2025-08-18T17:40:36+08:00"
 *   Authoring_Subagent: "pdfjs-config-expert"
 *   Principle_Applied: "集中配置与错误处理原则"
 *   Quality_Check: "统一的PDF.js配置管理，确保Worker正确加载。"
 * }}
 */

import * as pdfjsLib from 'pdfjs-dist'

// PDF.js配置接口
interface PdfJsConfig {
  workerSrc: string
  verbosity: number
  maxImageSize: number
  isEvalSupported: boolean
  disableFontFace: boolean
}

// 默认配置
const DEFAULT_CONFIG: PdfJsConfig = {
  workerSrc: '',
  verbosity: 0, // 0: 错误, 1: 警告, 5: 信息
  maxImageSize: 16777216, // 16MB
  isEvalSupported: false, // 安全考虑
  disableFontFace: false
}

// 获取Worker路径
function getWorkerPath(): string {
  // 优先使用CDN路径，确保可用性
  try {
    const version = pdfjsLib.version || '3.11.174'
    return `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`
  } catch {
    // 备用CDN路径
    return 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
  }
}

// 获取CDN备用路径
function getCdnWorkerPath(): string {
  try {
    const version = pdfjsLib.version || '3.11.174'
    return `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`
  } catch {
    return 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
  }
}

// {{RIPER-5+SMART-6:
//   Action: "Critical-Fix"
//   Task_ID: "PDFJS-WORKER-CONFIG-FIX"
//   Timestamp: "2025-08-19T09:27:00+08:00"
//   Authoring_Subagent: "pdfjs-config-expert"
//   Principle_Applied: "安全配置与错误处理原则"
//   Quality_Check: "修复PDF.js Worker配置问题，避免对象不可扩展错误。"
// }}

// 安全配置PDF.js
export function configurePdfJs(customConfig?: Partial<PdfJsConfig>): boolean {
  try {
    console.log('🔧 开始配置PDF.js...')

    // 检查PDF.js是否正确加载
    if (!pdfjsLib) {
      console.error('❌ PDF.js未正确导入')
      return false
    }

    // 合并配置
    const config = { ...DEFAULT_CONFIG, ...customConfig }

    // 安全配置Worker路径 - 避免对象不可扩展错误
    try {
      const workerPath = config.workerSrc || getWorkerPath()

      // 检查GlobalWorkerOptions是否存在且可配置
      if (pdfjsLib.GlobalWorkerOptions) {
        // 尝试直接设置
        if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
          pdfjsLib.GlobalWorkerOptions.workerSrc = workerPath
          console.log('✅ PDF.js Worker路径已设置:', workerPath)
        }
      } else {
        // 如果不存在，创建新对象
        ;(pdfjsLib as any).GlobalWorkerOptions = {
          workerSrc: workerPath,
          verbosity: config.verbosity
        }
        console.log('✅ PDF.js GlobalWorkerOptions已创建:', workerPath)
      }
    } catch (workerError) {
      console.warn('⚠️ Worker配置失败，尝试备用方案:', workerError)
      return configureWorkerFallback()
    }

    // 验证配置
    const isConfigured = !!(pdfjsLib.GlobalWorkerOptions?.workerSrc)
    if (isConfigured) {
      console.log('✅ PDF.js配置成功')
    } else {
      console.error('❌ PDF.js配置失败，尝试备用方案')
      return configureWorkerFallback()
    }

    return isConfigured

  } catch (error) {
    console.error('❌ PDF.js配置过程中发生错误:', error)
    return configureWorkerFallback()
  }
}

// 配置备用Worker
export function configureWorkerFallback(): boolean {
  try {
    console.log('🔄 配置PDF.js Worker备用方案...')

    const cdnPath = getCdnWorkerPath()

    // 尝试多种配置方式
    try {
      // 方式1：直接设置
      if (pdfjsLib.GlobalWorkerOptions) {
        pdfjsLib.GlobalWorkerOptions.workerSrc = cdnPath
      } else {
        ;(pdfjsLib as any).GlobalWorkerOptions = { workerSrc: cdnPath }
      }
      console.log('✅ PDF.js Worker备用方案配置成功:', cdnPath)
      return true
    } catch (configError) {
      console.warn('⚠️ 标准配置失败，尝试强制配置:', configError)

      // 方式2：强制重新创建GlobalWorkerOptions
      try {
        delete (pdfjsLib as any).GlobalWorkerOptions
        ;(pdfjsLib as any).GlobalWorkerOptions = { workerSrc: cdnPath }
        console.log('✅ PDF.js Worker强制配置成功:', cdnPath)
        return true
      } catch (forceError) {
        console.error('❌ 强制配置也失败:', forceError)

        // 最后的备用方案：禁用Worker
        try {
          ;(pdfjsLib as any).disableWorker = true
          console.warn('⚠️ 已禁用PDF.js Worker，使用主线程模式')
          return true
        } catch {
          return false
        }
      }
    }

  } catch (error) {
    console.error('❌ PDF.js Worker备用方案配置失败:', error)
    return false
  }
}

// 检查PDF.js是否可用
export function checkPdfJsAvailability(): boolean {
  try {
    // 检查基本API
    const hasBasicApi = !!(
      pdfjsLib &&
      pdfjsLib.getDocument &&
      pdfjsLib.version
    )

    // 检查Worker配置
    const hasWorkerConfig = !!(
      pdfjsLib.GlobalWorkerOptions &&
      pdfjsLib.GlobalWorkerOptions.workerSrc
    )

    const isAvailable = hasBasicApi && hasWorkerConfig

    console.log('📊 PDF.js可用性检查:', {
      hasBasicApi,
      hasWorkerConfig,
      isAvailable,
      version: pdfjsLib.version,
      workerSrc: pdfjsLib.GlobalWorkerOptions?.workerSrc
    })

    return isAvailable

  } catch (error) {
    console.error('❌ PDF.js可用性检查失败:', error)
    return false
  }
}

// 初始化PDF.js (自动调用)
export function initializePdfJs(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      // 首先尝试标准配置
      let success = configurePdfJs()

      if (!success) {
        console.warn('⚠️ 标准配置失败，尝试备用方案...')
        success = configureWorkerFallback()
      }

      // 验证配置
      if (success) {
        success = checkPdfJsAvailability()
      }

      if (success) {
        console.log('🎉 PDF.js初始化成功')
      } else {
        console.error('❌ PDF.js初始化失败')
      }

      resolve(success)

    } catch (error) {
      console.error('❌ PDF.js初始化过程中发生错误:', error)
      resolve(false)
    }
  })
}

// 获取PDF.js信息
export function getPdfJsInfo() {
  return {
    version: pdfjsLib.version,
    workerSrc: pdfjsLib.GlobalWorkerOptions?.workerSrc,
    verbosity: pdfjsLib.GlobalWorkerOptions?.verbosity,
    isConfigured: checkPdfJsAvailability()
  }
}

// 导出PDF.js实例 (已配置)
export { pdfjsLib }

// 自动初始化 (在模块加载时)
let initPromise: Promise<boolean> | null = null

export function ensurePdfJsInitialized(): Promise<boolean> {
  if (!initPromise) {
    initPromise = initializePdfJs()
  }
  return initPromise
}

// 立即开始初始化
ensurePdfJsInitialized().then(success => {
  if (!success) {
    console.warn('⚠️ PDF.js自动初始化失败，请手动调用 ensurePdfJsInitialized()')
  }
})
