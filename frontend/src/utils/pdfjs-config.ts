/**
 * {{RIPER-5+SMART-6:
 *   Action: "PDF.js-Configuration"
 *   Task_ID: "PDFJS-WORKER-CONFIG"
 *   Timestamp: "2025-08-18T17:40:36+08:00"
 *   Authoring_Subagent: "pdfjs-config-expert"
 *   Principle_Applied: "集中配置与错误处理原则"
 *   Quality_Check: "统一的PDF.js配置管理，确保Worker正确加载。"
 * }}
 */

import * as pdfjsLib from 'pdfjs-dist'

// PDF.js配置接口
interface PdfJsConfig {
  workerSrc: string
  verbosity: number
  maxImageSize: number
  isEvalSupported: boolean
  disableFontFace: boolean
}

// 默认配置
const DEFAULT_CONFIG: PdfJsConfig = {
  workerSrc: '',
  verbosity: 0, // 0: 错误, 1: 警告, 5: 信息
  maxImageSize: 16777216, // 16MB
  isEvalSupported: false, // 安全考虑
  disableFontFace: false
}

// 获取Worker路径
function getWorkerPath(): string {
  // 开发环境和生产环境的Worker路径
  const paths = {
    development: [
      '/node_modules/pdfjs-dist/build/pdf.worker.min.js',
      '/node_modules/pdfjs-dist/build/pdf.worker.js'
    ],
    production: [
      '/pdfjs/build/pdf.worker.min.js',
      '/assets/pdf.worker.min.js'
    ]
  }

  const isDev = import.meta.env.DEV
  const candidatePaths = isDev ? paths.development : paths.production

  // 返回第一个可用路径
  return candidatePaths[0]
}

// 获取CDN备用路径
function getCdnWorkerPath(): string {
  try {
    const version = pdfjsLib.version || '3.11.174'
    return `https://cdnjs.cloudflare.com/ajax/libs/pdf.js/${version}/pdf.worker.min.js`
  } catch {
    return 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'
  }
}

// 安全配置PDF.js
export function configurePdfJs(customConfig?: Partial<PdfJsConfig>): boolean {
  try {
    console.log('🔧 开始配置PDF.js...')

    // 检查PDF.js是否正确加载
    if (!pdfjsLib) {
      console.error('❌ PDF.js未正确导入')
      return false
    }

    // 确保GlobalWorkerOptions存在
    if (!pdfjsLib.GlobalWorkerOptions) {
      console.warn('⚠️ GlobalWorkerOptions未定义，创建默认对象')
      ;(pdfjsLib as any).GlobalWorkerOptions = {}
    }

    // 合并配置
    const config = { ...DEFAULT_CONFIG, ...customConfig }

    // 配置Worker路径
    if (!pdfjsLib.GlobalWorkerOptions.workerSrc) {
      const workerPath = config.workerSrc || getWorkerPath()
      pdfjsLib.GlobalWorkerOptions.workerSrc = workerPath
      console.log('✅ PDF.js Worker路径已设置:', workerPath)
    }

    // 设置其他配置
    if (typeof pdfjsLib.GlobalWorkerOptions.verbosity === 'undefined') {
      pdfjsLib.GlobalWorkerOptions.verbosity = config.verbosity
    }

    // 验证配置
    const isConfigured = !!pdfjsLib.GlobalWorkerOptions.workerSrc
    if (isConfigured) {
      console.log('✅ PDF.js配置成功')
    } else {
      console.error('❌ PDF.js配置失败')
    }

    return isConfigured

  } catch (error) {
    console.error('❌ PDF.js配置过程中发生错误:', error)
    return false
  }
}

// 配置备用Worker
export function configureWorkerFallback(): boolean {
  try {
    console.log('🔄 配置PDF.js Worker备用方案...')

    if (!pdfjsLib.GlobalWorkerOptions) {
      ;(pdfjsLib as any).GlobalWorkerOptions = {}
    }

    const cdnPath = getCdnWorkerPath()
    pdfjsLib.GlobalWorkerOptions.workerSrc = cdnPath
    
    console.log('✅ 备用Worker路径已设置:', cdnPath)
    return true

  } catch (error) {
    console.error('❌ 备用Worker配置失败:', error)
    return false
  }
}

// 检查PDF.js是否可用
export function checkPdfJsAvailability(): boolean {
  try {
    // 检查基本API
    const hasBasicApi = !!(
      pdfjsLib &&
      pdfjsLib.getDocument &&
      pdfjsLib.version
    )

    // 检查Worker配置
    const hasWorkerConfig = !!(
      pdfjsLib.GlobalWorkerOptions &&
      pdfjsLib.GlobalWorkerOptions.workerSrc
    )

    const isAvailable = hasBasicApi && hasWorkerConfig

    console.log('📊 PDF.js可用性检查:', {
      hasBasicApi,
      hasWorkerConfig,
      isAvailable,
      version: pdfjsLib.version,
      workerSrc: pdfjsLib.GlobalWorkerOptions?.workerSrc
    })

    return isAvailable

  } catch (error) {
    console.error('❌ PDF.js可用性检查失败:', error)
    return false
  }
}

// 初始化PDF.js (自动调用)
export function initializePdfJs(): Promise<boolean> {
  return new Promise((resolve) => {
    try {
      // 首先尝试标准配置
      let success = configurePdfJs()

      if (!success) {
        console.warn('⚠️ 标准配置失败，尝试备用方案...')
        success = configureWorkerFallback()
      }

      // 验证配置
      if (success) {
        success = checkPdfJsAvailability()
      }

      if (success) {
        console.log('🎉 PDF.js初始化成功')
      } else {
        console.error('❌ PDF.js初始化失败')
      }

      resolve(success)

    } catch (error) {
      console.error('❌ PDF.js初始化过程中发生错误:', error)
      resolve(false)
    }
  })
}

// 获取PDF.js信息
export function getPdfJsInfo() {
  return {
    version: pdfjsLib.version,
    workerSrc: pdfjsLib.GlobalWorkerOptions?.workerSrc,
    verbosity: pdfjsLib.GlobalWorkerOptions?.verbosity,
    isConfigured: checkPdfJsAvailability()
  }
}

// 导出PDF.js实例 (已配置)
export { pdfjsLib }

// 自动初始化 (在模块加载时)
let initPromise: Promise<boolean> | null = null

export function ensurePdfJsInitialized(): Promise<boolean> {
  if (!initPromise) {
    initPromise = initializePdfJs()
  }
  return initPromise
}

// 立即开始初始化
ensurePdfJsInitialized().then(success => {
  if (!success) {
    console.warn('⚠️ PDF.js自动初始化失败，请手动调用 ensurePdfJsInitialized()')
  }
})
